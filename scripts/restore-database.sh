#!/bin/bash

# Supabase Database Restore Script (CLI Only)
# Usage: ./scripts/restore-database.sh [backup_timestamp]

set -e

# Configuration
BACKUP_DIR="backups/database"

# Function to display usage
usage() {
    echo "Usage: $0 [backup_timestamp]"
    echo ""
    echo "Examples:"
    echo "  $0 20240115_143022"
    echo "  $0 (will show available backups)"
    echo ""
    echo "Available backups:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -la "$BACKUP_DIR"/ | grep -E "backup_info_.*\.txt" | sed 's/.*backup_info_/  /' | sed 's/\.txt$//'
    else
        echo "  No backups found in $BACKUP_DIR"
    fi
    exit 1
}

# Check if timestamp is provided
if [ -z "$1" ]; then
    echo "❌ Error: No backup timestamp provided"
    usage
fi

TIMESTAMP="$1"
BACKUP_INFO_FILE="$BACKUP_DIR/backup_info_$TIMESTAMP.txt"
FULL_BACKUP_FILE="$BACKUP_DIR/supabase_full_$TIMESTAMP.sql"
SCHEMA_BACKUP_FILE="$BACKUP_DIR/supabase_schema_$TIMESTAMP.sql"
DATA_BACKUP_FILE="$BACKUP_DIR/supabase_data_$TIMESTAMP.sql"
ROLES_BACKUP_FILE="$BACKUP_DIR/supabase_roles_$TIMESTAMP.sql"

echo "🔄 Starting Supabase database restore using CLI..."

# Check if backup files exist
if [ ! -f "$BACKUP_INFO_FILE" ]; then
    echo "❌ Error: Backup info file not found: $BACKUP_INFO_FILE"
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/ | grep -E "backup_info_.*\.txt" | sed 's/.*backup_info_/  /' | sed 's/\.txt$//'
    exit 1
fi

# Display backup information
echo "📋 Backup Information:"
cat "$BACKUP_INFO_FILE"
echo ""

# Check if Supabase is running
if ! supabase status > /dev/null 2>&1; then
    echo "❌ Error: Supabase is not running"
    echo "Please start Supabase with: supabase start"
    exit 1
fi

# Warning prompt
echo "⚠️  WARNING: This will completely replace your current database!"
echo "Are you sure you want to continue? (yes/no)"
read -r CONFIRM

if [ "$CONFIRM" != "yes" ]; then
    echo "❌ Restore cancelled"
    exit 1
fi

# Create a backup of current state before restore
echo "💾 Creating backup of current database state before restore..."
CURRENT_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
supabase db dump --local -f "$BACKUP_DIR/pre_restore_backup_$CURRENT_TIMESTAMP.sql"

echo "💾 Pre-restore backup saved as: pre_restore_backup_$CURRENT_TIMESTAMP.sql"

# Choose restore method
echo "🎯 Select restore method:"
echo "1. Full restore (Complete backup file - recommended)"
echo "2. Component restore (Schema + Data + Roles separately)"
echo "3. Schema only restore"
echo "4. Data only restore"
echo "Enter choice (1-4): "
read -r RESTORE_METHOD

case $RESTORE_METHOD in
    1)
        if [ ! -f "$FULL_BACKUP_FILE" ]; then
            echo "❌ Error: Full backup file not found: $FULL_BACKUP_FILE"
            exit 1
        fi
        
        echo "🗄️  Resetting database and applying full backup..."
        supabase db reset --local
        
        echo "🔄 Applying full backup..."
        PGPASSWORD=postgres psql \
            -h 127.0.0.1 \
            -p 54322 \
            -U postgres \
            -d postgres \
            --quiet \
            < "$FULL_BACKUP_FILE"
        ;;
    2)
        echo "🗄️  Resetting database and applying component backups..."
        supabase db reset --local
        
        if [ -f "$SCHEMA_BACKUP_FILE" ]; then
            echo "📋 Applying schema backup..."
            PGPASSWORD=postgres psql \
                -h 127.0.0.1 \
                -p 54322 \
                -U postgres \
                -d postgres \
                --quiet \
                < "$SCHEMA_BACKUP_FILE"
        fi
        
        if [ -f "$DATA_BACKUP_FILE" ]; then
            echo "📊 Applying data backup..."
            PGPASSWORD=postgres psql \
                -h 127.0.0.1 \
                -p 54322 \
                -U postgres \
                -d postgres \
                --quiet \
                < "$DATA_BACKUP_FILE"
        fi
        
        if [ -f "$ROLES_BACKUP_FILE" ]; then
            echo "👥 Applying roles backup..."
            PGPASSWORD=postgres psql \
                -h 127.0.0.1 \
                -p 54322 \
                -U postgres \
                -d postgres \
                --quiet \
                < "$ROLES_BACKUP_FILE"
        fi
        ;;
    3)
        if [ ! -f "$SCHEMA_BACKUP_FILE" ]; then
            echo "❌ Error: Schema backup file not found: $SCHEMA_BACKUP_FILE"
            exit 1
        fi
        
        echo "🗄️  Resetting database and applying schema backup..."
        supabase db reset --local
        
        echo "📋 Applying schema backup..."
        PGPASSWORD=postgres psql \
            -h 127.0.0.1 \
            -p 54322 \
            -U postgres \
            -d postgres \
            --quiet \
            < "$SCHEMA_BACKUP_FILE"
        ;;
    4)
        if [ ! -f "$DATA_BACKUP_FILE" ]; then
            echo "❌ Error: Data backup file not found: $DATA_BACKUP_FILE"
            exit 1
        fi
        
        echo "📊 Applying data backup (keeping current schema)..."
        PGPASSWORD=postgres psql \
            -h 127.0.0.1 \
            -p 54322 \
            -U postgres \
            -d postgres \
            --quiet \
            < "$DATA_BACKUP_FILE"
        ;;
    *)
        echo "❌ Error: Invalid choice"
        exit 1
        ;;
esac

# Verify restore
echo "🔍 Verifying restore..."
TABLE_COUNT=$(PGPASSWORD=postgres psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

if [ "$TABLE_COUNT" -gt 0 ]; then
    echo "✅ Restore completed successfully!"
    echo "📊 Database contains $TABLE_COUNT tables"
    echo "🔄 You may need to restart your application to pick up the changes"
else
    echo "⚠️  Restore completed but no tables found. Please check the logs."
fi

echo ""
echo "📁 Pre-restore backup saved as: $BACKUP_DIR/pre_restore_backup_$CURRENT_TIMESTAMP.sql"
echo "🏷️  Restored from timestamp: $TIMESTAMP"
echo ""
echo "Next steps:"
echo "1. Check your application to ensure everything is working"
echo "2. Run 'supabase status' to verify all services are running"
echo "3. Visit http://127.0.0.1:54323 to check the database in Studio" 
