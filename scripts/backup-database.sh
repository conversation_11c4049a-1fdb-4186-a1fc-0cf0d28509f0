#!/bin/bash

# Supabase Database Backup Script (CLI Only)
# Usage: ./scripts/backup-database.sh

set -e

# Configuration
BACKUP_DIR="backups/database"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "🗄️  Starting Supabase database backup using CLI..."

# Check if Supabase is running
if ! supabase status > /dev/null 2>&1; then
    echo "❌ Error: Supabase is not running"
    echo "Please start Supabase with: supabase start"
    exit 1
fi

# 1. Full schema backup
echo "📋 Creating schema backup..."
supabase db dump --local -f "$BACKUP_DIR/supabase_schema_$TIMESTAMP.sql"

# 2. Data-only backup
echo "📊 Creating data-only backup..."
supabase db dump --local --data-only --use-copy -f "$BACKUP_DIR/supabase_data_$TIMESTAMP.sql"

# 3. Roles backup
echo "👥 Creating roles backup..."
supabase db dump --local --role-only -f "$BACKUP_DIR/supabase_roles_$TIMESTAMP.sql"

# 4. Combined backup for easy restore
echo "📦 Creating combined backup..."
supabase db dump --local -f "$BACKUP_DIR/supabase_full_$TIMESTAMP.sql"

# 5. Create backup metadata
echo "📝 Creating backup metadata..."
cat > "$BACKUP_DIR/backup_info_$TIMESTAMP.txt" << EOF
Backup Information
==================
Date: $(date)
Timestamp: $TIMESTAMP
Method: Supabase CLI
Project: Börsen Blick
Git Branch: $(git branch --show-current 2>/dev/null || echo "unknown")
Git Commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

Supabase Status:
$(supabase status)

Files Created:
- supabase_schema_$TIMESTAMP.sql (Schema only)
- supabase_data_$TIMESTAMP.sql (Data only)
- supabase_roles_$TIMESTAMP.sql (Roles only)
- supabase_full_$TIMESTAMP.sql (Complete backup)
- backup_info_$TIMESTAMP.txt (This file)

Restore Commands:
================
For full restore:
1. supabase db reset --local
2. psql -h 127.0.0.1 -p 54322 -U postgres -d postgres < supabase_full_$TIMESTAMP.sql

For selective restore:
1. supabase db reset --local
2. psql -h 127.0.0.1 -p 54322 -U postgres -d postgres < supabase_schema_$TIMESTAMP.sql
3. psql -h 127.0.0.1 -p 54322 -U postgres -d postgres < supabase_data_$TIMESTAMP.sql
4. psql -h 127.0.0.1 -p 54322 -U postgres -d postgres < supabase_roles_$TIMESTAMP.sql
EOF

# 6. Get backup file sizes
echo "📏 Backup file sizes:"
ls -lh "$BACKUP_DIR"/*_$TIMESTAMP*

echo "✅ Backup completed successfully!"
echo "📁 Backup files saved to: $BACKUP_DIR"
echo "🏷️  Backup timestamp: $TIMESTAMP"
echo ""
echo "To restore from this backup:"
echo "1. Stop Supabase: supabase stop"
echo "2. Start fresh: supabase start"
echo "3. Reset database: supabase db reset --local"
echo "4. Apply backup: psql -h 127.0.0.1 -p 54322 -U postgres -d postgres < $BACKUP_DIR/supabase_full_$TIMESTAMP.sql" 
