#!/usr/bin/env node
/* eslint-disable */

/**
 * Comprehensive Database Seeding Script
 *
 * Populates the database with all essential content:
 * - Admin user account
 * - Keywords for content filtering
 * - Categories for article organisation
 * - RSS feeds for content sources
 * - Header navigation
 * - Footer configuration
 * - Sample pages (if needed)
 *
 * Safe to run multiple times (idempotent)
 *
 * Usage: node scripts/seed-database.mjs
 * Or: pnpm db:seed
 */

import { getPayload } from 'payload';
import { config as dotenvConfig } from 'dotenv';
import path from 'path';

// Load environment variables FIRST, before importing payload config
dotenvConfig({ path: '.env.local' });
dotenvConfig({ path: '.env' });

// Debug environment loading
console.log(
  `🔍 PAYLOAD_SECRET loaded: ${process.env.PAYLOAD_SECRET ? 'YES' : 'NO'}`
);
console.log(
  `🔍 PAYLOAD_SECRET length: ${process.env.PAYLOAD_SECRET?.length || 0}`
);

if (!process.env.PAYLOAD_SECRET || process.env.PAYLOAD_SECRET.trim() === '') {
  console.error('❌ PAYLOAD_SECRET not found or empty in environment');
  console.error(
    '💡 Make sure .env.local contains: PAYLOAD_SECRET=your_secret_key'
  );
  process.exit(1);
}

const startTime = Date.now();

console.log('🌱 Starting database seeding...\n');

try {
  console.log('🔧 Loading PayloadCMS configuration...');

  // Dynamically import config after environment variables are loaded
  const { default: config } = await import('../src/payload.config.ts');
  console.log(`🔍 Config secret: ${config.secret ? 'SET' : 'MISSING'}`);

  const payload = await getPayload({ config });

  let adminUserCreated = 0;
  let keywordsCreated = 0;
  let categoriesCreated = 0;
  let feedsCreated = 0;
  let headerCreated = 0;
  let footerCreated = 0;

  // Step 1: Create Admin User
  console.log('👤 Creating admin user...');
  try {
    const existingUsers = await payload.find({
      collection: 'users',
      limit: 1,
    });

    if (existingUsers.totalDocs === 0) {
      await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'UME2qzp*qyh*nyf3vag',
          name: 'Shane Griffiths',
        },
      });
      adminUserCreated++;
      console.log('   ✅ Created admin user: <EMAIL>');
    } else {
      console.log(`   ⚠️ Admin user exists: ${existingUsers.docs[0].email}`);
    }
  } catch (error) {
    console.error('   ❌ Failed to create admin user:', error.message);
  }

  // Step 2: Keywords for content filtering
  console.log('📝 Creating keywords...');
  const keywords = [
    // German financial keywords
    {
      keyword: 'Aktie',
      germanKeyword: 'Aktie',
      englishKeyword: 'stock',
      isActive: true,
    },
    {
      keyword: 'Börse',
      germanKeyword: 'Börse',
      englishKeyword: 'stock exchange',
      isActive: true,
    },
    {
      keyword: 'Finanzen',
      germanKeyword: 'Finanzen',
      englishKeyword: 'finance',
      isActive: true,
    },
    {
      keyword: 'Wirtschaft',
      germanKeyword: 'Wirtschaft',
      englishKeyword: 'economy',
      isActive: true,
    },
    {
      keyword: 'Unternehmen',
      germanKeyword: 'Unternehmen',
      englishKeyword: 'company',
      isActive: true,
    },
    {
      keyword: 'Investition',
      germanKeyword: 'Investition',
      englishKeyword: 'investment',
      isActive: true,
    },
    {
      keyword: 'Bank',
      germanKeyword: 'Bank',
      englishKeyword: 'bank',
      isActive: true,
    },
    {
      keyword: 'Gewinn',
      germanKeyword: 'Gewinn',
      englishKeyword: 'profit',
      isActive: true,
    },
    {
      keyword: 'Verlust',
      germanKeyword: 'Verlust',
      englishKeyword: 'loss',
      isActive: true,
    },
    {
      keyword: 'Dividende',
      germanKeyword: 'Dividende',
      englishKeyword: 'dividend',
      isActive: true,
    },
    {
      keyword: 'Quartal',
      germanKeyword: 'Quartal',
      englishKeyword: 'quarter',
      isActive: true,
    },
    {
      keyword: 'Umsatz',
      germanKeyword: 'Umsatz',
      englishKeyword: 'revenue',
      isActive: true,
    },
    {
      keyword: 'Bilanz',
      germanKeyword: 'Bilanz',
      englishKeyword: 'balance sheet',
      isActive: true,
    },
    {
      keyword: 'Handel',
      germanKeyword: 'Handel',
      englishKeyword: 'trading',
      isActive: true,
    },
    {
      keyword: 'Markt',
      germanKeyword: 'Markt',
      englishKeyword: 'market',
      isActive: true,
    },
    {
      keyword: 'Fusion',
      germanKeyword: 'Fusion',
      englishKeyword: 'merger',
      isActive: true,
    },
    {
      keyword: 'Übernahme',
      germanKeyword: 'Übernahme',
      englishKeyword: 'acquisition',
      isActive: true,
    },
    {
      keyword: 'IPO',
      germanKeyword: 'Börsengang',
      englishKeyword: 'IPO',
      isActive: true,
    },
    {
      keyword: 'Kryptowährung',
      germanKeyword: 'Kryptowährung',
      englishKeyword: 'cryptocurrency',
      isActive: true,
    },
    {
      keyword: 'Inflation',
      germanKeyword: 'Inflation',
      englishKeyword: 'inflation',
      isActive: true,
    },
  ];

  for (const keywordData of keywords) {
    try {
      const existingKeyword = await payload.find({
        collection: 'keywords',
        where: { keyword: { equals: keywordData.keyword } },
        limit: 1,
      });

      if (existingKeyword.totalDocs === 0) {
        await payload.create({
          collection: 'keywords',
          data: keywordData,
        });
        keywordsCreated++;
        console.log(`   ✅ Created keyword: ${keywordData.keyword}`);
      } else {
        console.log(`   ⚠️ Keyword exists: ${keywordData.keyword}`);
      }
    } catch (error) {
      console.error(
        `   ❌ Failed to create keyword ${keywordData.keyword}:`,
        error.message
      );
    }
  }

  // Step 3: Categories for article organisation
  console.log('\n📂 Creating categories...');
  const categories = [
    { title: 'Wirtschaft', english: 'Economy', slug: 'wirtschaft' },
    { title: 'Technologie', english: 'Technology', slug: 'technologie' },
    { title: 'Finanzen', english: 'Finance', slug: 'finanzen' },
    { title: 'Unternehmen', english: 'Companies', slug: 'unternehmen' },
    { title: 'Märkte', english: 'Markets', slug: 'maerkte' },
    {
      title: 'Kryptowährungen',
      english: 'Cryptocurrencies',
      slug: 'kryptowaehrungen',
    },
    { title: 'Immobilien', english: 'Real Estate', slug: 'immobilien' },
    { title: 'Politik', english: 'Politics', slug: 'politik' },
  ];

  for (const categoryData of categories) {
    try {
      const existingCategory = await payload.find({
        collection: 'categories',
        where: { slug: { equals: categoryData.slug } },
        limit: 1,
      });

      if (existingCategory.totalDocs === 0) {
        await payload.create({
          collection: 'categories',
          data: categoryData,
        });
        categoriesCreated++;
        console.log(`   ✅ Created category: ${categoryData.title}`);
      } else {
        console.log(`   ⚠️ Category exists: ${categoryData.title}`);
      }
    } catch (error) {
      console.error(
        `   ❌ Failed to create category ${categoryData.title}:`,
        error.message
      );
    }
  }

  // Step 4: RSS Feeds for content sources
  console.log('\n📡 Creating RSS feeds...');
  const rssFeeds = [
    {
      name: 'Handelsblatt',
      url: 'https://www.handelsblatt.com/contentexport/feed/schlagzeilen',
      isActive: true,
      category: 'wirtschaft',
      language: 'de',
      priority: 'high',
      processingFrequency: 'hourly',
    },
    {
      name: 'Der Aktionär',
      url: 'https://www.deraktionaer.de/rss.xml',
      isActive: true,
      category: 'finanzen',
      language: 'de',
      priority: 'high',
      processingFrequency: 'hourly',
    },
    {
      name: 'Finanzen.net',
      url: 'https://www.finanzen.net/rss/nachrichten',
      isActive: true,
      category: 'finanzen',
      language: 'de',
      priority: 'medium',
      processingFrequency: 'every-2-hours',
    },
    {
      name: 'OnVista',
      url: 'https://www.onvista.de/rss',
      isActive: true,
      category: 'maerkte',
      language: 'de',
      priority: 'medium',
      processingFrequency: 'every-3-hours',
    },
    {
      name: 'Wallstreet Online',
      url: 'https://www.wallstreet-online.de/rss/nachrichten-alle.xml',
      isActive: true,
      category: 'maerkte',
      language: 'de',
      priority: 'medium',
      processingFrequency: 'every-3-hours',
    },
  ];

  for (const feedData of rssFeeds) {
    try {
      const existingFeed = await payload.find({
        collection: 'rss-feeds',
        where: { url: { equals: feedData.url } },
        limit: 1,
      });

      if (existingFeed.totalDocs === 0) {
        await payload.create({
          collection: 'rss-feeds',
          data: feedData,
        });
        feedsCreated++;
        console.log(`   ✅ Created RSS feed: ${feedData.name}`);
      } else {
        console.log(`   ⚠️ RSS feed exists: ${feedData.name}`);
      }
    } catch (error) {
      console.error(
        `   ❌ Failed to create RSS feed ${feedData.name}:`,
        error.message
      );
    }
  }

  // Step 5: Header Navigation
  console.log('\n🧭 Creating header navigation...');
  try {
    const headerData = {
      navItems: [
        {
          link: {
            type: 'custom',
            url: '/',
            label: 'Home',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/categories/wirtschaft',
            label: 'Wirtschaft',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/categories/finanzen',
            label: 'Finanzen',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/categories/technologie',
            label: 'Technologie',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/categories/maerkte',
            label: 'Märkte',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/articles',
            label: 'Alle Artikel',
            newTab: false,
          },
        },
      ],
    };

    await payload.updateGlobal({
      slug: 'header',
      data: headerData,
      context: { disableRevalidate: true },
    });
    headerCreated++;
    console.log('   ✅ Created header navigation');
  } catch (error) {
    console.error('   ❌ Failed to create header navigation:', error.message);
  }

  // Step 6: Footer Configuration
  console.log('\n🦶 Creating footer configuration...');
  try {
    const footerData = {
      logo: {
        title: 'Börsen Blick',
        url: '/',
      },
      description:
        'Ihr verlässlicher Partner für aktuelle Finanznachrichten und Marktanalysen. Bleiben Sie informiert über die wichtigsten Entwicklungen an den Märkten.',
      navigationSections: [
        {
          title: 'Kategorien',
          links: [
            {
              link: {
                type: 'custom',
                url: '/categories/wirtschaft',
                label: 'Wirtschaft',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/categories/finanzen',
                label: 'Finanzen',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/categories/technologie',
                label: 'Technologie',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/categories/maerkte',
                label: 'Märkte',
                newTab: false,
              },
            },
          ],
        },
        {
          title: 'Service',
          links: [
            {
              link: {
                type: 'custom',
                url: '/articles',
                label: 'Alle Artikel',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/newsletter',
                label: 'Newsletter',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/kontakt',
                label: 'Kontakt',
                newTab: false,
              },
            },
          ],
        },
        {
          title: 'Unternehmen',
          links: [
            {
              link: {
                type: 'custom',
                url: '/ueber-uns',
                label: 'Über uns',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/team',
                label: 'Team',
                newTab: false,
              },
            },
            {
              link: {
                type: 'custom',
                url: '/karriere',
                label: 'Karriere',
                newTab: false,
              },
            },
          ],
        },
      ],
      socialLinks: [
        {
          platform: 'twitter',
          url: 'https://twitter.com/borsenblick',
          label: 'Folgen Sie uns auf Twitter',
        },
        {
          platform: 'linkedin',
          url: 'https://linkedin.com/company/borsenblick',
          label: 'Folgen Sie uns auf LinkedIn',
        },
        {
          platform: 'email',
          url: 'mailto:<EMAIL>',
          label: 'Kontaktieren Sie uns per E-Mail',
        },
      ],
      legalLinks: [
        {
          link: {
            type: 'custom',
            url: '/impressum',
            label: 'Impressum',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/datenschutz',
            label: 'Datenschutz',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/agb',
            label: 'AGB',
            newTab: false,
          },
        },
        {
          link: {
            type: 'custom',
            url: '/cookies',
            label: 'Cookie-Richtlinie',
            newTab: false,
          },
        },
      ],
      copyright: {
        companyName: 'börsenblick.de',
        customText: 'Alle Marktdaten und Analysen ohne Gewähr.',
      },
    };

    await payload.updateGlobal({
      slug: 'footer',
      data: footerData,
      context: { disableRevalidate: true },
    });
    footerCreated++;
    console.log('   ✅ Created footer configuration');
  } catch (error) {
    console.error(
      '   ❌ Failed to create footer configuration:',
      error.message
    );
  }

  // Final summary
  const processingTime = Date.now() - startTime;
  const processingTimeSeconds = Math.round(processingTime / 1000);

  console.log('\n🎯 Database seeding completed!');
  console.log(`   👤 Admin users created: ${adminUserCreated}`);
  console.log(`   📝 Keywords created: ${keywordsCreated}`);
  console.log(`   📂 Categories created: ${categoriesCreated}`);
  console.log(`   📡 RSS feeds created: ${feedsCreated}`);
  console.log(`   🧭 Header navigation: ${headerCreated ? '✅' : '⚠️'}`);
  console.log(`   🦶 Footer configuration: ${footerCreated ? '✅' : '⚠️'}`);
  console.log(`   ⏱️ Processing time: ${processingTimeSeconds}s`);

  // Get final database stats
  const [keywordStats, categoryStats, feedStats, articleStats] =
    await Promise.all([
      payload.find({ collection: 'keywords', limit: 1000 }),
      payload.find({ collection: 'categories', limit: 1000 }),
      payload.find({ collection: 'rss-feeds', limit: 1000 }),
      payload.find({ collection: 'articles', limit: 1000 }),
    ]);

  console.log('\n📊 Database totals:');
  console.log(`   Keywords: ${keywordStats.totalDocs}`);
  console.log(`   Categories: ${categoryStats.totalDocs}`);
  console.log(`   RSS Feeds: ${feedStats.totalDocs}`);
  console.log(`   Articles: ${articleStats.totalDocs}`);

  const readyForProcessing =
    feedStats.totalDocs > 0 && keywordStats.totalDocs > 0;
  console.log(
    `\n${readyForProcessing ? '✅' : '❌'} Ready for RSS processing: ${readyForProcessing}`
  );

  if (readyForProcessing) {
    console.log('\n🚀 Next steps:');
    console.log('   - Run pipeline: pnpm pipeline:test');
    console.log('   - Login to admin: http://localhost:3000/admin');
    console.log('   - Email: <EMAIL>');
    console.log('   - Check site: http://localhost:3000');
    console.log(
      '   - View categories: http://localhost:3000/categories/wirtschaft'
    );
  }

  process.exit(0);
} catch (error) {
  console.error('\n❌ Database seeding failed:', error.message);
  process.exit(1);
}
