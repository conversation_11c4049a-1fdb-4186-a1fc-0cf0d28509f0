/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    articles: Article;
    categories: Category;
    pages: Page;
    keywords: Keyword;
    'processed-urls': ProcessedUrl;
    'rss-feeds': RssFeed;
    media: Media;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    articles: ArticlesSelect<false> | ArticlesSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    pages: PagesSelect<false> | PagesSelect<true>;
    keywords: KeywordsSelect<false> | KeywordsSelect<true>;
    'processed-urls': ProcessedUrlsSelect<false> | ProcessedUrlsSelect<true>;
    'rss-feeds': RssFeedsSelect<false> | RssFeedsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {
    header: Header;
    footer: Footer;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  name?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles".
 */
export interface Article {
  id: number;
  /**
   * Article title - Used within the backend to identify the article. Not used in the frontend.
   */
  title: string;
  /**
   * URL-friendly version of the title
   */
  slug?: string | null;
  /**
   * Featured image for the article
   */
  featuredImage?: (number | null) | Media;
  /**
   * Type of article - determines source and workflow
   */
  articleType: 'generated' | 'curated';
  /**
   * Editorial stage
   */
  workflowStage?: ('curated-draft' | 'candidate-article' | 'translated' | 'ready-for-review') | null;
  /**
   * Article categories for organization and filtering
   */
  categories?: (number | Category)[] | null;
  /**
   * Article placement tier for prioritisation
   */
  placement?: ('tier-1' | 'tier-2' | 'tier-3') | null;
  /**
   * Pin this article for featured placement
   */
  pinned?: boolean | null;
  /**
   * Mark this article as trending (manual override)
   */
  trending?: boolean | null;
  /**
   * Pre-computed reading time in minutes (auto-calculated from content)
   */
  readTimeMinutes?: number | null;
  /**
   * Companies extracted from the article content
   */
  relatedCompanies?:
    | {
        name: string;
        /**
         * Stock ticker symbol (e.g., AAPL, MSFT)
         */
        ticker?: string | null;
        /**
         * Stock exchange code (e.g., NYSE, NASDAQ, LSE)
         */
        exchange?: string | null;
        relevance?: ('high' | 'medium' | 'low') | null;
        /**
         * AI confidence score (0-100)
         */
        confidence?: number | null;
        /**
         * Mark as a featured company for prominent display
         */
        featured?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * User who published this article
   */
  publishedBy?: (number | null) | User;
  /**
   * When this article was published
   */
  publishedAt?: string | null;
  /**
   * Indicates if content has been AI-enhanced
   */
  hasBeenEnhanced?: boolean | null;
  /**
   * Indicates if German translation is available
   */
  hasGermanTranslation?: boolean | null;
  /**
   * Indicates if this article originated from RSS feed and has source content
   */
  hasOriginalSource?: boolean | null;
  englishTab?: {
    /**
     * AI-enhanced English title optimised for international markets
     */
    enhancedTitle?: string | null;
    /**
     * AI-enhanced English summary (100-150 characters)
     */
    enhancedSummary?: string | null;
    /**
     * Enhanced English content for international markets
     */
    enhancedContent?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    /**
     * AI-enhanced keywords for English content
     */
    keywords?:
      | {
          keyword?: string | null;
          id?: string | null;
        }[]
      | null;
    /**
     * AI-enhanced key insights highlighting the most important takeaways
     */
    enhancedKeyInsights?:
      | {
          insight: string;
          id?: string | null;
        }[]
      | null;
  };
  sourcesTab?: {
    /**
     * The RSS feed that provided this article
     */
    sourceFeed?: (number | null) | RssFeed;
    /**
     * Original article URL from RSS feed
     */
    sourceUrl?: string | null;
    /**
     * When the original source article was published
     */
    originalPublishedAt?: string | null;
    /**
     * Original article title from the source
     */
    originalTitle?: string | null;
    /**
     * Original article summary from Firecrawl extraction
     */
    originalSummary?: string | null;
    /**
     * Original scraped content from Firecrawl, converted to Lexical format
     */
    originalContent?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
  };
  germanTab?: {
    /**
     * AI-translated German title. This is the main title of the article.
     */
    germanTitle?: string | null;
    /**
     * AI-translated German summary (150-200 words). Used in the main Article Desc
     */
    germanSummary?: string | null;
    /**
     * AI-translated German content (600-750 words)
     */
    germanContent?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    /**
     * AI-translated German keywords
     */
    germanKeywords?:
      | {
          keyword?: string | null;
          id?: string | null;
        }[]
      | null;
    /**
     * AI-translated German key insights
     */
    germanKeyInsights?:
      | {
          insight: string;
          id?: string | null;
        }[]
      | null;
  };
  meta?: {
    title?: string | null;
    description?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
  };
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Upload and manage images for articles
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  /**
   * Alternative text for accessibility and SEO
   */
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    feature?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: number;
  /**
   * Category name in German (main language)
   */
  title: string;
  english: string;
  /**
   * URL-friendly identifier
   */
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage RSS feed sources for content ingestion
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "rss-feeds".
 */
export interface RssFeed {
  id: number;
  /**
   * A descriptive name for this RSS feed (e.g., "Finanzen.net News")
   */
  name: string;
  /**
   * The full URL of the RSS feed
   */
  url: string;
  /**
   * If checked, this feed will be processed. Uncheck to temporarily disable
   */
  isActive?: boolean | null;
  /**
   * Primary language of this feed
   */
  language?: ('de' | 'en') | null;
  /**
   * Timestamp of the last time this feed was processed
   */
  lastProcessed?: string | null;
  /**
   * Timestamp of the last time the system attempted to process this feed
   */
  lastChecked?: string | null;
  /**
   * Timestamp of the last time the system successfully processed this feed and found articles
   */
  lastSuccessfulCheck?: string | null;
  /**
   * Total number of items processed from this feed
   */
  itemsProcessed?: number | null;
  /**
   * Total number of items accepted from this feed
   */
  itemsAccepted?: number | null;
  /**
   * Number of new articles found during the last successful check
   */
  articlesFoundSinceLastSuccessful?: number | null;
  /**
   * Total number of articles from this feed that have been accepted into the system
   */
  totalArticlesAccepted?: number | null;
  /**
   * Number of consecutive times processing this feed has failed. Resets on success
   */
  errorCount?: number | null;
  /**
   * Details of the last error encountered while processing this feed
   */
  lastErrorMessage?: string | null;
  /**
   * How often this feed should be checked for new articles (in minutes)
   */
  processingFrequency?: number | null;
  /**
   * Processing priority for this feed
   */
  priority?: ('low' | 'medium' | 'high') | null;
  /**
   * Override global Firecrawl settings for this specific feed
   */
  firecrawlOptions?: {
    /**
     * Remove base64 images to reduce content size (leave unchecked to use global default)
     */
    removeBase64Images?: boolean | null;
    /**
     * Enable ad-blocking and cookie popup blocking (leave unchecked to use global default)
     */
    blockAds?: boolean | null;
    /**
     * HTML tags/selectors to exclude from extraction (e.g., ".advertisement", "#paywall")
     */
    excludeTags?:
      | {
          tag: string;
          id?: string | null;
        }[]
      | null;
    /**
     * If specified, only extract content from these HTML tags/selectors
     */
    includeTags?:
      | {
          tag: string;
          id?: string | null;
        }[]
      | null;
  };
  /**
   * Custom keyword filtering rules for this feed
   */
  keywordFiltering?: {
    /**
     * Require exact keyword matches (more restrictive filtering)
     */
    strictKeywordMatching?: boolean | null;
    /**
     * Additional keywords specific to this feed (supplements global keywords)
     */
    customKeywords?:
      | {
          keyword: string;
          englishKeyword: string;
          /**
           * Importance of this keyword (1=low, 10=high)
           */
          weight?: number | null;
          id?: string | null;
        }[]
      | null;
  };
  /**
   * Control how articles from this feed are processed
   */
  processingOptions?: {
    /**
     * Maximum number of articles to scrape with Firecrawl per run (after keyword filtering)
     */
    maxFirecrawlScrape?: number | null;
    /**
     * Maximum number of successfully scraped articles to send to OpenAI for processing
     */
    maxArticlesPerRun?: number | null;
    /**
     * Skip automatic translation for articles from this feed
     */
    skipTranslation?: boolean | null;
    /**
     * Skip AI content enhancement for articles from this feed
     */
    skipEnhancement?: boolean | null;
    /**
     * Override default timeout for content extraction (leave empty to use site defaults)
     */
    customTimeout?: number | null;
    /**
     * Always use stealth mode for this feed (useful for sites with anti-bot protection)
     */
    enableStealth?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage static pages for the website. Create pages like About Us, Contact, Privacy Policy, etc.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: number;
  title: string;
  /**
   * Indicates if German translation is available
   */
  hasGermanTranslation?: boolean | null;
  englishTab: {
    /**
     * English page title for translation
     */
    title: string;
    /**
     * The main English content of the page
     */
    content: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    };
  };
  germanTab?: {
    /**
     * AI-translated German title for the page
     */
    germanTitle?: string | null;
    /**
     * AI-translated German content for the page
     */
    germanContent?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
  };
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  /**
   * When this page was published
   */
  publishedAt?: string | null;
  /**
   * URL-friendly identifier
   */
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Keywords used for RSS feed filtering and article relevance scoring
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "keywords".
 */
export interface Keyword {
  id: number;
  /**
   * German keyword used for RSS feed filtering
   */
  keyword: string;
  /**
   * English translation of the keyword
   */
  englishKeyword: string;
  /**
   * Whether this keyword is actively used for filtering
   */
  isActive?: boolean | null;
  /**
   * Number of articles that matched this keyword
   */
  usageCount?: number | null;
  /**
   * Optional description or notes about this keyword
   */
  description?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Tracks URLs processed from RSS feeds to prevent duplicates
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "processed-urls".
 */
export interface ProcessedUrl {
  id: number;
  /**
   * The article URL that was processed
   */
  url: string;
  /**
   * Current processing status of this URL
   */
  status: 'pending' | 'accepted' | 'rejected' | 'error';
  /**
   * Title of the article from RSS feed
   */
  title?: string | null;
  /**
   * When the article was originally published (from RSS feed)
   */
  publicationDate?: string | null;
  /**
   * RSS feed that provided this URL (null if feed was deleted)
   */
  feedId?: (number | null) | RssFeed;
  /**
   * When this URL was last processed
   */
  processedAt?: string | null;
  /**
   * Details about processing result or error
   */
  reason?: string | null;
  /**
   * The article created from this URL (if accepted)
   */
  articleId?: (number | null) | Article;
  /**
   * Number of times processing was attempted
   */
  attemptCount?: number | null;
  /**
   * When the last processing attempt was made
   */
  lastAttemptAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'articles';
        value: number | Article;
      } | null)
    | ({
        relationTo: 'categories';
        value: number | Category;
      } | null)
    | ({
        relationTo: 'pages';
        value: number | Page;
      } | null)
    | ({
        relationTo: 'keywords';
        value: number | Keyword;
      } | null)
    | ({
        relationTo: 'processed-urls';
        value: number | ProcessedUrl;
      } | null)
    | ({
        relationTo: 'rss-feeds';
        value: number | RssFeed;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles_select".
 */
export interface ArticlesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  featuredImage?: T;
  articleType?: T;
  workflowStage?: T;
  categories?: T;
  placement?: T;
  pinned?: T;
  trending?: T;
  readTimeMinutes?: T;
  relatedCompanies?:
    | T
    | {
        name?: T;
        ticker?: T;
        exchange?: T;
        relevance?: T;
        confidence?: T;
        featured?: T;
        id?: T;
      };
  publishedBy?: T;
  publishedAt?: T;
  hasBeenEnhanced?: T;
  hasGermanTranslation?: T;
  hasOriginalSource?: T;
  englishTab?:
    | T
    | {
        enhancedTitle?: T;
        enhancedSummary?: T;
        enhancedContent?: T;
        keywords?:
          | T
          | {
              keyword?: T;
              id?: T;
            };
        enhancedKeyInsights?:
          | T
          | {
              insight?: T;
              id?: T;
            };
      };
  sourcesTab?:
    | T
    | {
        sourceFeed?: T;
        sourceUrl?: T;
        originalPublishedAt?: T;
        originalTitle?: T;
        originalSummary?: T;
        originalContent?: T;
      };
  germanTab?:
    | T
    | {
        germanTitle?: T;
        germanSummary?: T;
        germanContent?: T;
        germanKeywords?:
          | T
          | {
              keyword?: T;
              id?: T;
            };
        germanKeyInsights?:
          | T
          | {
              insight?: T;
              id?: T;
            };
      };
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  title?: T;
  english?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  hasGermanTranslation?: T;
  englishTab?:
    | T
    | {
        title?: T;
        content?: T;
      };
  germanTab?:
    | T
    | {
        germanTitle?: T;
        germanContent?: T;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "keywords_select".
 */
export interface KeywordsSelect<T extends boolean = true> {
  keyword?: T;
  englishKeyword?: T;
  isActive?: T;
  usageCount?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "processed-urls_select".
 */
export interface ProcessedUrlsSelect<T extends boolean = true> {
  url?: T;
  status?: T;
  title?: T;
  publicationDate?: T;
  feedId?: T;
  processedAt?: T;
  reason?: T;
  articleId?: T;
  attemptCount?: T;
  lastAttemptAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "rss-feeds_select".
 */
export interface RssFeedsSelect<T extends boolean = true> {
  name?: T;
  url?: T;
  isActive?: T;
  language?: T;
  lastProcessed?: T;
  lastChecked?: T;
  lastSuccessfulCheck?: T;
  itemsProcessed?: T;
  itemsAccepted?: T;
  articlesFoundSinceLastSuccessful?: T;
  totalArticlesAccepted?: T;
  errorCount?: T;
  lastErrorMessage?: T;
  processingFrequency?: T;
  priority?: T;
  firecrawlOptions?:
    | T
    | {
        removeBase64Images?: T;
        blockAds?: T;
        excludeTags?:
          | T
          | {
              tag?: T;
              id?: T;
            };
        includeTags?:
          | T
          | {
              tag?: T;
              id?: T;
            };
      };
  keywordFiltering?:
    | T
    | {
        strictKeywordMatching?: T;
        customKeywords?:
          | T
          | {
              keyword?: T;
              englishKeyword?: T;
              weight?: T;
              id?: T;
            };
      };
  processingOptions?:
    | T
    | {
        maxFirecrawlScrape?: T;
        maxArticlesPerRun?: T;
        skipTranslation?: T;
        skipEnhancement?: T;
        customTimeout?: T;
        enableStealth?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        feature?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: number;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'articles';
                value: number | Article;
              } | null)
            | ({
                relationTo: 'categories';
                value: number | Category;
              } | null)
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: number;
  /**
   * Configure the footer logo and branding
   */
  logo: {
    /**
     * Upload logo image (optional)
     */
    image?: (number | null) | Media;
    /**
     * Logo text/title
     */
    title: string;
    /**
     * URL when logo is clicked
     */
    url?: string | null;
  };
  /**
   * Brief description or tagline for the footer
   */
  description?: string | null;
  /**
   * Main footer navigation sections (max 3 columns)
   */
  navigationSections?:
    | {
        /**
         * Section title (e.g., "Product", "Company", "Resources")
         */
        title: string;
        links?:
          | {
              link: {
                type?: ('reference' | 'custom') | null;
                newTab?: boolean | null;
                reference?:
                  | ({
                      relationTo: 'articles';
                      value: number | Article;
                    } | null)
                  | ({
                      relationTo: 'categories';
                      value: number | Category;
                    } | null)
                  | ({
                      relationTo: 'pages';
                      value: number | Page;
                    } | null);
                url?: string | null;
                label: string;
              };
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Social media links and icons
   */
  socialLinks?:
    | {
        /**
         * Choose the social media platform
         */
        platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'github' | 'threads' | 'email';
        /**
         * Full URL to your social media profile
         */
        url: string;
        /**
         * Accessibility label (e.g., "Follow us on Instagram")
         */
        label: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Legal and privacy links displayed at the bottom
   */
  legalLinks?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'articles';
                value: number | Article;
              } | null)
            | ({
                relationTo: 'categories';
                value: number | Category;
              } | null)
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  /**
   * Copyright information (year is automatically current year)
   */
  copyright: {
    /**
     * Company name for copyright
     */
    companyName: string;
    /**
     * Additional text after "All rights reserved" (optional)
     */
    customText?: string | null;
  };
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  logo?:
    | T
    | {
        image?: T;
        title?: T;
        url?: T;
      };
  description?: T;
  navigationSections?:
    | T
    | {
        title?: T;
        links?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                  };
              id?: T;
            };
        id?: T;
      };
  socialLinks?:
    | T
    | {
        platform?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  legalLinks?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  copyright?:
    | T
    | {
        companyName?: T;
        customText?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}