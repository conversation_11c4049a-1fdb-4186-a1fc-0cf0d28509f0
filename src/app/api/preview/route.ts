import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';

export async function GET(request: Request): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const previewSecret = searchParams.get('previewSecret');
  const slug = searchParams.get('slug');
  const collection = searchParams.get('collection');
  const path = searchParams.get('path');

  // Check the secret and required parameters
  if (
    previewSecret !== process.env.PAYLOAD_PUBLIC_DRAFT_SECRET ||
    !slug ||
    collection !== 'articles'
  ) {
    return new Response('Invalid token', { status: 401 });
  }

  // Enable Draft Mode
  const draft = await draftMode();
  draft.enable();

  // Redirect to the article page using the path parameter
  redirect(path || `/articles/${slug}`);
}
