/**
 * Pages Translation API Route
 *
 * Provides German translation functionality for Pages collection
 * using the generic translation handler for code reuse.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleTranslationRequest } from '@/lib/api/translation-handler';
import { withAuth } from '@/lib/auth/simple-auth';

/**
 * Internal POST handler (before auth wrapper)
 */
async function _POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Use the generic translation handler for Pages collection
    return await handleTranslationRequest(request, 'pages');
  } catch (error) {
    console.error('❌ Error in Pages translation route:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * Export authenticated POST route
 * Pages translation requires authentication (only editors can translate)
 */
export const POST = withAuth(_POST);

/**
 * Health check endpoint for Pages translation
 */
export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    status: 'healthy',
    service: 'pages-translation',
    timestamp: new Date().toISOString(),
    collection: 'pages',
    features: [
      'German translation',
      'Save-first workflow',
      'Lexical content preservation',
      'Form field updates',
    ],
  });
}
