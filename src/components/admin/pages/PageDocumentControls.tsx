/**
 * Page Document Controls Component
 *
 * Provides translation functionality specifically for Pages collection
 * using the generic TranslationControls component. This is a thin wrapper
 * that configures the generic component for Pages-specific needs.
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-01-27
 */

'use client';

import React from 'react';
import { useDocumentInfo } from '@payloadcms/ui';
import { TranslationControls } from '../shared/TranslationControls';

/**
 * Page-specific validation context
 */
interface PageValidationContext {
  collection: 'pages';
  fields: {
    title: string;
    content: any;
    summary?: string;
    hasGermanTranslation: boolean;
  };
}

/**
 * Additional validation rules specific to Pages
 */
function validatePageForTranslation(context: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Pages-specific validation rules
  if (!context.fields.title || context.fields.title.length < 10) {
    errors.push(
      'Page title must be at least 10 characters for meaningful translation'
    );
  }

  if (!context.fields.content) {
    errors.push('Page content is required for translation');
  }

  // Check for minimum content length
  const hasMinimumContent = context.fields.title.length >= 20;
  if (!hasMinimumContent) {
    errors.push(
      'Page must have substantial content (20+ characters) for translation'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Handle translation completion for Pages
 */
function handlePageTranslationComplete(result: any): void {
  console.log('✅ Page translation completed:', {
    success: result.success,
    hasGermanContent: !!result.data?.germanTab?.germanTitle,
    processingTime: result.metrics?.processingTime,
  });

  // Pages don't have workflow stages like Articles, so no additional processing needed
  // The generic component handles all the form updates
}

/**
 * Page Document Controls Component
 */
export const PageDocumentControls: React.FC = () => {
  console.log('🔧 PageDocumentControls component is loading...');

  const docInfo = useDocumentInfo();
  const { id } = docInfo;

  console.log('📄 Document info:', { id, docInfo });

  // Always show something visible for testing
  return (
    <div
      style={{
        padding: '15px',
        border: '2px solid #2196F3',
        backgroundColor: '#f0f8ff',
        borderRadius: '5px',
        margin: '10px 0',
      }}
    >
      <h4 style={{ color: '#2196F3', margin: '0 0 10px 0' }}>
        📄 Page Translation Controls
      </h4>
      <p style={{ margin: '5px 0', fontSize: '12px' }}>
        Document ID: {id || 'Not found'}
      </p>

      {!id ? (
        <p style={{ color: '#666', fontSize: '12px' }}>
          Auto-save will create the document, then translation will be
          available.
        </p>
      ) : (
        <div>
          <p
            style={{ color: '#4CAF50', fontSize: '12px', marginBottom: '10px' }}
          >
            ✅ Ready for translation!
          </p>
          <TranslationControls
            collection="pages"
            apiEndpoint="/api/pages/translate"
            additionalValidation={validatePageForTranslation}
            onTranslationComplete={handlePageTranslationComplete}
          />
        </div>
      )}
    </div>
  );
};

export default PageDocumentControls;
