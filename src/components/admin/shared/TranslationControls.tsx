/**
 * Generic Translation Controls Component
 *
 * Provides translation functionality for any PayloadCMS collection
 * that supports bilingual English/German content. Extracted from
 * ArticleDocumentControls to enable reuse across collections.
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-01-27
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import {
  useDocumentInfo,
  useAllFormFields,
  useFormFields,
} from '@payloadcms/ui';
// Types only - no server imports in client component
export type TranslationCollection = 'articles' | 'pages';
import { useArticleNotifications } from '../notifications/ArticleNotifications';

/**
 * Translation context for different collections
 */
interface TranslationValidationContext {
  collection: TranslationCollection;
  fields: {
    title: string;
    content: any;
    summary?: string;
    hasGermanTranslation: boolean;
  };
}

/**
 * Translation Controls Props
 */
interface TranslationControlsProps {
  collection: TranslationCollection;
  apiEndpoint?: string; // Optional custom endpoint, defaults to /api/{collection}/translate
  fieldMapping?: {
    titleField?: string;
    contentField?: string;
    summaryField?: string;
    hasTranslationFlag?: string;
  };
  additionalValidation?: (context: TranslationValidationContext) => {
    isValid: boolean;
    errors: string[];
  };
  onTranslationComplete?: (result: any) => void;
  customNotifications?: {
    showTranslationSuccess: (hasExisting: boolean) => void;
    showOperationError: (operation: string, error: string) => void;
    showValidationError: (message: string) => void;
    showProcessingInfo: (operation: string) => void;
  };
}

/**
 * Standard API Response format
 */
interface StandardAPIResponse {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  metrics?: any;
}

/**
 * Generic Translation Controls Component
 */
export const TranslationControls: React.FC<TranslationControlsProps> = ({
  collection,
  apiEndpoint,
  fieldMapping,
  additionalValidation,
  onTranslationComplete,
  customNotifications,
}) => {
  console.log('🌐 TranslationControls loading for collection:', collection);

  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  // Use provided notifications or default to ArticleNotifications
  const defaultNotifications = useArticleNotifications();
  const notifications = customNotifications || defaultNotifications;

  // Translation state management
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveJustCompleted, setSaveJustCompleted] = useState(false);
  const [operationProgress, setOperationProgress] = useState<{
    operation: 'translate' | null;
    stage: string;
    progress: number;
  }>({ operation: null, stage: '', progress: 0 });

  // Progress tracking helper
  const updateOperationProgress = useCallback(
    (operation: 'translate', stage: string, progress: number) => {
      setOperationProgress({ operation, stage, progress });
    },
    []
  );

  // Field mapping for different collections (client-side only)
  const getFieldMapping = (collection: TranslationCollection) => {
    return collection === 'articles'
      ? {
          titleField: 'englishTab.enhancedTitle',
          contentField: 'englishTab.enhancedContent',
          summaryField: 'englishTab.enhancedSummary',
          hasTranslationFlag: 'hasGermanTranslation',
        }
      : {
          titleField: 'englishTab.title',
          contentField: 'englishTab.content',
          summaryField: undefined,
          hasTranslationFlag: 'hasGermanTranslation',
        };
  };

  const mapping = getFieldMapping(collection);

  // Get field values using hooks at the top level
  const title = useFormFields(([fields]) => {
    if (mapping.titleField.includes('.')) {
      const [tab, field] = mapping.titleField.split('.');
      return fields[`${tab}.${field}`]?.value || fields.title?.value || '';
    }
    return fields[mapping.titleField]?.value || fields.title?.value || '';
  }) as string;

  const content = useFormFields(([fields]) => {
    if (mapping.contentField.includes('.')) {
      const [tab, field] = mapping.contentField.split('.');
      return fields[`${tab}.${field}`]?.value;
    }
    return fields[mapping.contentField]?.value;
  });

  const summary = useFormFields(([fields]) => {
    if (mapping.summaryField?.includes('.')) {
      const [tab, field] = mapping.summaryField.split('.');
      return fields[`${tab}.${field}`]?.value;
    }
    return mapping.summaryField
      ? fields[mapping.summaryField]?.value
      : undefined;
  }) as string | undefined;

  const hasGermanTranslation = useFormFields(([fields]) => {
    if (mapping.hasTranslationFlag.includes('.')) {
      const [tab, field] = mapping.hasTranslationFlag.split('.');
      return fields[`${tab}.${field}`]?.value || false;
    }
    return fields[mapping.hasTranslationFlag]?.value || false;
  }) as boolean;

  // Combine field values
  const fieldValues = { title, content, summary, hasGermanTranslation };

  // Form dirty state detection
  const formIsDirty = Object.values(fields).some(
    (field: any) => field?.hasValidated && field?.modified
  );

  // Create validation context
  const validationContext: TranslationValidationContext = useMemo(
    () => ({
      collection,
      fields: fieldValues,
    }),
    [collection, fieldValues]
  );

  // Validate content for translation
  const validateForTranslation = useCallback(() => {
    const translatableContent = {
      id: String(id || ''),
      collection,
      title: fieldValues.title,
      content: fieldValues.content,
      summary: fieldValues.summary,
      hasGermanTranslation: fieldValues.hasGermanTranslation,
    };

    // Simple client-side validation
    const validation = {
      isValid: !!(
        fieldValues.title &&
        fieldValues.title.length >= 5 &&
        fieldValues.content
      ),
      errors: [] as string[],
      buttonText: fieldValues.hasGermanTranslation
        ? 'Re-Translate to German'
        : 'Translate to German',
    };

    if (!fieldValues.title || fieldValues.title.length < 5) {
      validation.errors.push('Title must be at least 5 characters long');
      validation.isValid = false;
    }

    if (!fieldValues.content) {
      validation.errors.push('Content is required for translation');
      validation.isValid = false;
    }

    // Run additional validation if provided
    if (additionalValidation) {
      const additionalResult = additionalValidation(validationContext);
      if (!additionalResult.isValid) {
        validation.errors.push(...additionalResult.errors);
      }
    }

    return validation;
  }, [id, collection, fieldValues, additionalValidation, validationContext]);

  // Check if translation is possible
  const canTranslate = useMemo(() => {
    if (!id || formIsDirty || isSaving || isTranslating) {
      return false;
    }

    const validation = validateForTranslation();
    return validation.isValid;
  }, [id, formIsDirty, isSaving, isTranslating, validateForTranslation]);

  // Get validation message
  const getValidationMessage = useCallback(() => {
    if (!id) return 'Please save the document first';
    if (isSaving)
      return 'Saving in progress... Please wait for save to complete';
    if (saveJustCompleted)
      return 'Save completed! Processing will be available in a moment';
    if (formIsDirty) return 'Please save your changes before processing';

    const validation = validateForTranslation();
    return validation.errors[0] || 'Ready for translation';
  }, [id, formIsDirty, isSaving, saveJustCompleted, validateForTranslation]);

  // Update form fields after translation
  const updateFormAfterTranslation = useCallback(
    (responseData: StandardAPIResponse) => {
      const mapping = getFieldMapping(collection);

      // Build updates array based on collection type
      const updates: Array<{ path: string; value: any }> = [
        // German tab updates
        {
          path: 'germanTab.germanTitle',
          value: responseData.data?.germanTab?.germanTitle,
        },
        {
          path: 'germanTab.germanContent',
          value: responseData.data?.germanTab?.germanContent,
        },
        // Translation flag
        {
          path: mapping.hasTranslationFlag,
          value: responseData.data?.[mapping.hasTranslationFlag],
        },
      ];

      // Add summary for collections that support it (Articles only)
      if (
        collection === 'articles' &&
        responseData.data?.germanTab?.germanSummary
      ) {
        updates.push({
          path: 'germanTab.germanSummary',
          value: responseData.data?.germanTab?.germanSummary,
        });
      }

      // Articles-specific fields
      if (collection === 'articles') {
        updates.push(
          {
            path: 'germanTab.germanKeyInsights',
            value: responseData.data?.germanTab?.germanKeyInsights,
          },
          {
            path: 'germanTab.germanKeywords',
            value: responseData.data?.germanTab?.germanKeywords,
          },
          {
            path: 'workflowStage',
            value: responseData.data?.workflowStage,
          }
        );
      }

      // Apply all updates
      updates.forEach(update => {
        if (update.value !== undefined) {
          dispatchFields({
            type: 'UPDATE',
            path: update.path,
            value: update.value,
          });
        }
      });

      // Force tab visibility re-evaluation by toggling hasGermanTranslation
      setTimeout(() => {
        dispatchFields({
          type: 'UPDATE',
          path: mapping.hasTranslationFlag,
          value: false,
        });

        setTimeout(() => {
          dispatchFields({
            type: 'UPDATE',
            path: mapping.hasTranslationFlag,
            value: responseData.data?.[mapping.hasTranslationFlag],
          });
        }, 50);
      }, 100);
    },
    [collection, dispatchFields]
  );

  // Translation handler
  const handleTranslation = useCallback(async () => {
    if (!canTranslate) {
      const validationMessage = getValidationMessage();
      notifications.showValidationError(
        validationMessage || 'Cannot translate content at this time'
      );
      return;
    }

    setIsTranslating(true);
    updateOperationProgress('translate', 'Syncing with database...', 10);

    // Show processing notification
    notifications.showProcessingInfo('translation');

    try {
      updateOperationProgress(
        'translate',
        'Ensuring fresh content access...',
        30
      );

      // Build API endpoint
      const endpoint = apiEndpoint || `/api/${collection}/translate`;

      // Prepare request body
      const requestBody = {
        documentId: id,
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      updateOperationProgress('translate', 'Processing translation...', 70);
      const result: StandardAPIResponse = await response.json();

      if (result.success && result.data) {
        updateOperationProgress('translate', 'Updating form fields...', 90);

        // Show success feedback to user
        notifications.showTranslationSuccess(fieldValues.hasGermanTranslation);

        // Update form fields
        updateFormAfterTranslation(result);

        // Set immediate visual feedback
        setTranslationJustCompleted(true);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);

        // Call completion callback if provided
        if (onTranslationComplete) {
          onTranslationComplete(result);
        }

        // Force page refresh to ensure German tab appears
        console.log('🔄 Forcing page refresh to show German tab...');
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        console.error('Translation API Error:', result);
        notifications.showOperationError(
          'translation',
          result.error || 'An unknown error occurred during translation'
        );
      }
    } catch (error) {
      console.error('Translation Network Error:', error);
      notifications.showOperationError(
        'translation',
        'Failed to communicate with the translation service'
      );
    } finally {
      setIsTranslating(false);
      setOperationProgress({ operation: null, stage: '', progress: 0 });
    }
  }, [
    canTranslate,
    id,
    collection,
    apiEndpoint,
    fieldValues.hasGermanTranslation,
    getValidationMessage,
    updateFormAfterTranslation,
    onTranslationComplete,
    notifications,
  ]);

  // Button state and styling
  const isTranslationDisabled =
    !canTranslate ||
    isTranslating ||
    translationJustCompleted ||
    saveJustCompleted;

  const getTranslationButtonText = () => {
    if (isSaving) return 'Saving... Please wait';
    if (saveJustCompleted) return 'Save complete! Ready to translate';
    if (isTranslating) {
      return operationProgress.operation === 'translate' &&
        operationProgress.stage
        ? `${operationProgress.stage} (${operationProgress.progress}%)`
        : 'Translating to German...';
    }
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';

    const validation = validateForTranslation();
    return validation.buttonText || 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isSaving) return '#6B7280'; // Gray for saving
    if (saveJustCompleted) return '#10B981'; // Green for save complete
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (fieldValues.hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  // Don't render if no document ID
  if (!id) {
    return null;
  }

  return (
    <div style={{ marginBottom: '16px' }}>
      <button
        onClick={handleTranslation}
        disabled={isTranslationDisabled}
        title={
          !id
            ? 'Please save the document first before translation'
            : !canTranslate
              ? getValidationMessage()
              : undefined
        }
        style={{
          backgroundColor: getTranslationButtonColor(),
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          padding: '12px 20px',
          cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
          opacity: isTranslationDisabled ? 0.6 : 1,
          fontSize: '14px',
          fontWeight: '500',
          minWidth: '160px',
          minHeight: '40px',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          margin: '0',
          width: '100%',
        }}
      >
        {isTranslating && (
          <div
            style={{
              width: '14px',
              height: '14px',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              borderTop: '2px solid white',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              flexShrink: 0,
            }}
          />
        )}
        {translationJustCompleted && (
          <div
            style={{
              width: '14px',
              height: '14px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0,
            }}
          >
            ✓
          </div>
        )}
        {getTranslationButtonText()}
      </button>

      {/* Add spinner animation CSS */}
      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default TranslationControls;
