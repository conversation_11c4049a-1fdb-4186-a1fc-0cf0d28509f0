'use client';

import { memo } from 'react';
import { TriangleAlert } from 'lucide-react';
import NewsCard, { CardSkeleton } from '@/components/NewsCard';
import type { Article } from '@/payload-types';

interface TierSectionProps {
  tier: 'tier-1' | 'tier-2' | 'tier-3';
  articles: Article[];
  title: string;
  variant: 'featured' | 'horizontal' | 'title-only';
  isLoading?: boolean;
  className?: string;
  locale?: 'en' | 'de';
  maxArticles?: number;
  emptyStateMessage?: string;
  showMoreButton?: boolean;
  onShowMore?: () => void;
  showDescriptionForFirst?: number;
}

const TierSection = memo(
  ({
    tier,
    articles,
    title,
    variant,
    isLoading = false,
    className = '',
    locale = 'en',
    maxArticles,
    emptyStateMessage = 'No articles available',
    showMoreButton = false,
    onShowMore,
    showDescriptionForFirst,
  }: TierSectionProps) => {
    const displayArticles = maxArticles
      ? articles.slice(0, maxArticles)
      : articles;
    const hasMoreArticles = maxArticles && articles.length > maxArticles;

    // Loading state
    if (isLoading) {
      return (
        <section className={className} aria-label={title} aria-busy="true">
          <div className="space-y-4">
            {Array.from({ length: variant === 'featured' ? 3 : 6 }).map(
              (_, i) => (
                <CardSkeleton
                  key={i}
                  variant={
                    variant === 'featured'
                      ? 'default'
                      : variant === 'horizontal'
                        ? 'horizontal-left'
                        : 'title-only'
                  }
                />
              )
            )}
          </div>
        </section>
      );
    }

    // Empty state
    if (displayArticles.length === 0) {
      return (
        <section className={className} aria-label={title}>
          <div className="text-center py-8 md:py-12">
            <div className="text-muted-foreground">
              <TriangleAlert className="size-4 mx-auto mb-3" />
              <p className="text-xs mb-2">{emptyStateMessage}</p>
              <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
            </div>
          </div>
        </section>
      );
    }

    // Featured layout (Tier 1)
    if (variant === 'featured') {
      return (
        <section className={className} aria-label={title}>
          {/* Hero Card */}
          <div className="mb-3 md:mb-4">
            <NewsCard
              article={displayArticles[0]}
              variant="default"
              showDescription={true}
              priority={true}
              locale={locale}
              className="shadow-sm"
            />
            {displayArticles[0].pinned && (
              <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                <span className="size-2 bg-blue-500 rounded-full animate-pulse" />
                <span>Wichtiger Artikel</span>
              </div>
            )}
          </div>

          {/* Featured Cards Grid */}
          {displayArticles.slice(1).length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-3 lg:gap-4">
              {displayArticles.slice(1).map((article, index) => (
                <NewsCard
                  key={article.id}
                  article={article}
                  variant="default"
                  showDescription={true}
                  locale={locale}
                  priority={index === 0} // Priority for second article
                />
              ))}
            </div>
          )}

          {/* Show More Button */}
          {(showMoreButton || hasMoreArticles) && (
            <div className="mt-3 text-left">
              <button
                onClick={onShowMore}
                className="text-xs font-medium text-primary hover:text-primary/80 transition-colors"
                aria-label={`Show more ${title.toLowerCase()}`}
              >
                Mehr wichtige Artikel anzeigen (
                {articles.length - displayArticles.length} weitere)
              </button>
            </div>
          )}
        </section>
      );
    }

    // Horizontal layout (Tier 2)
    if (variant === 'horizontal') {
      return (
        <section className={className} aria-label={title}>
          <div className="space-y-1.5 md:space-y-2 lg:space-y-3">
            {displayArticles.map(article => (
              <NewsCard
                key={article.id}
                article={article}
                variant="horizontal-left"
                showDescription={true}
                locale={locale}
              />
            ))}
          </div>

          {/* Show More Button */}
          {(showMoreButton || hasMoreArticles) && (
            <div className="mt-2 text-center">
              <button
                onClick={onShowMore}
                className="text-xs font-normal text-muted-foreground hover:text-primary/80 transition-colors"
                aria-label={`Show more ${title.toLowerCase()}`}
              >
                Mehr anzeigen ({articles.length - displayArticles.length}{' '}
                weitere)
              </button>
            </div>
          )}
        </section>
      );
    }

    // Title-only layout (Tier 3)
    return (
      <section className={className} aria-label={title}>
        <div className="space-y-1.5 md:space-y-2">
          {displayArticles.map((article, index) => (
            <NewsCard
              key={article.id}
              article={article}
              variant="title-only"
              showDescription={
                showDescriptionForFirst
                  ? index < showDescriptionForFirst
                  : false
              }
              locale={locale}
            />
          ))}
        </div>

        {/* Show More Button */}
        {(showMoreButton || hasMoreArticles) && (
          <div className="mt-2 text-center">
            <button
              onClick={onShowMore}
              className="text-xs font-normal text-muted-foreground hover:text-primary/80 transition-colors"
              aria-label={`Show more ${title.toLowerCase()}`}
            >
              Mehr anzeigen ({articles.length - displayArticles.length} weitere)
            </button>
          </div>
        )}
      </section>
    );
  }
);

TierSection.displayName = 'TierSection';

export default TierSection;
