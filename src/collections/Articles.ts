import type { CollectionConfig } from 'payload';
import { anyone } from '../access/anyone';
import { authenticated } from '../access/authenticated';
import {
  MetaTitleField,
  MetaDescriptionField,
  MetaImageField,
  PreviewField,
  OverviewField,
} from '@payloadcms/plugin-seo/fields';
import { authenticatedOrPublished } from '../access/authenticatedOrPublished';
import { defaultLexical } from '../fields/defaultLexical';
import {
  prepareLexicalForReading,
  prepareLexicalForStorage,
} from '../lib/utils/lexical-validation';
import { formatSlug } from '../fields/slug/formatSlug';
import {
  validateArticleForPublication,
  formatValidationError,
  logValidationWarnings,
} from '../lib/utils/article-validation';
import { calculateArticleReadingTime } from '../lib/utils/readtime'; // ✅ PHASE 2: Import readtime utility
import { createArticleInvalidationHook } from '../lib/cache/invalidation'; // ✅ PHASE 2: Import cache invalidation

export const Articles: CollectionConfig = {
  slug: 'articles',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'articleType', '_status', 'updatedAt'], // Fixed field names
    group: 'Content',
    // Add preview configuration using PayloadCMS pattern
    preview: ({ slug, collection }) => {
      if (!slug) return null;

      const params = new URLSearchParams({
        slug: String(slug),
        collection: 'articles',
        path: `/articles/${String(slug)}`,
        previewSecret: process.env.PAYLOAD_PUBLIC_DRAFT_SECRET || '',
      });

      return `/api/preview?${params.toString()}`;
    },
  },
  fields: [
    // FIXED: Simplified title logic using beforeValidate
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description:
          'Article title - Used within the backend to identify the article. Not used in the frontend.',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            try {
              // Only auto-populate if no title provided
              if (!value || (typeof value === 'string' && !value.trim())) {
                // Priority: German title > English title > existing value
                return (
                  data?.germanTab?.germanTitle ||
                  data?.englishTab?.enhancedTitle ||
                  value
                );
              }
              return value;
            } catch (error) {
              console.error('Error in title hook:', error);
              return value; // Fallback to existing value
            }
          },
        ],
      },
    },
    // Slug field - moved to main area under title
    {
      name: 'slug',
      type: 'text',
      admin: {
        description: 'URL-friendly version of the title',
      },
      hooks: {
        beforeValidate: [
          ({ value, data, operation }) => {
            try {
              // On create: Generate slug from enhanced English title or main title (no more pending slugs)
              if (!value && operation === 'create') {
                const titleSource =
                  data?.englishTab?.enhancedTitle || data?.title;

                if (titleSource) {
                  return formatSlug(titleSource);
                }
              }

              // On update: Switch to German slug when translation is added
              if (
                operation === 'update' &&
                data?.germanTab?.germanTitle &&
                data?.hasGermanTranslation
              ) {
                return formatSlug(data.germanTab.germanTitle);
              }

              // On update: Update to English enhanced title if it becomes available and no German exists
              if (
                operation === 'update' &&
                data?.englishTab?.enhancedTitle &&
                !data?.hasGermanTranslation &&
                !value
              ) {
                return formatSlug(data.englishTab.enhancedTitle);
              }

              return value;
            } catch (error) {
              console.error('Error in slug generation:', error);
              return value; // Fallback to existing value
            }
          },
        ],
      },
    },
    // Featured Image field
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the article',
      },
      filterOptions: {
        mimeType: { contains: 'image' },
      },
    },

    // Translate button at the very top of sidebar
    {
      type: 'ui',
      name: 'documentControls',
      admin: {
        position: 'sidebar',
        components: {
          Field:
            '@/components/admin/article-actions/DocumentControls#ArticleDocumentControls',
        },
      },
    },
    // 1. Article Type at top of sidebar
    {
      name: 'articleType',
      type: 'select',
      required: true,
      defaultValue: 'curated',
      options: [
        { label: 'Generated (from RSS)', value: 'generated' },
        { label: 'Curated (manual)', value: 'curated' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Type of article - determines source and workflow',
      },
    },
    // 2. Workflow Stage (optional editorial bookmark - publication uses native Draft/Publish)
    {
      name: 'workflowStage',
      label: 'Editorial Stage (Optional)',
      type: 'select',
      defaultValue: 'curated-draft',
      options: [
        { label: 'Curated Draft', value: 'curated-draft' },
        { label: 'Enhanced English (Candidate)', value: 'candidate-article' },
        { label: 'Enhanced & Translated', value: 'translated' },
        { label: 'Ready for Publication', value: 'ready-for-review' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Editorial stage',
      },
      hooks: {
        beforeChange: [
          ({ value, data }) => {
            // Set appropriate default workflow stage based on article type
            if (!value) {
              return data?.articleType === 'curated'
                ? 'curated-draft'
                : 'candidate-article';
            }
            return value;
          },
        ],
      },
    },
    // 3. Categories
    {
      name: 'categories',
      type: 'relationship',
      relationTo: 'categories',
      hasMany: true,
      admin: {
        position: 'sidebar',
        description: 'Article categories for organization and filtering',
      },
    },
    // 4. Placement
    {
      name: 'placement',
      type: 'select',
      options: [
        { label: 'Tier 1', value: 'tier-1' },
        { label: 'Tier 2', value: 'tier-2' },
        { label: 'Tier 3', value: 'tier-3' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Article placement tier for prioritisation',
      },
    },
    // 5. Pinned
    {
      name: 'pinned',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        description: 'Pin this article for featured placement',
      },
    },
    // 6. Trending (manual)
    {
      name: 'trending',
      type: 'checkbox',
      defaultValue: false,
      index: true, // Enable database indexing for efficient querying
      // Note: Could add custom validation here for business rules
      // Example: Only allow trending for published articles
      access: {
        read: () => true, // Anyone can read trending status
        update: ({ req: { user } }) => Boolean(user), // Only authenticated users can modify
      },
      admin: {
        position: 'sidebar',
        description: 'Mark this article as trending (manual override)',
      },
    },
    // ✅ PHASE 2: Pre-computed read time field
    {
      name: 'readTimeMinutes',
      label: 'Reading Time (Minutes)',
      type: 'number',
      min: 1,
      max: 60,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description:
          'Pre-computed reading time in minutes (auto-calculated from content)',
      },
    },
    // 8. Publication readiness indicator
    {
      name: 'publicationReadiness',
      type: 'ui',
      admin: {
        position: 'sidebar',
        components: {
          Field: './src/components/admin/PublicationReadinessIndicator',
        },
      },
    },
    // 9. Related Companies
    {
      name: 'relatedCompanies',
      label: 'Related Companies',
      type: 'array',
      fields: [
        {
          name: 'name',
          label: 'Company Name',
          type: 'text',
          required: true,
        },
        {
          name: 'ticker',
          label: 'Ticker Symbol',
          type: 'text',
          admin: {
            description: 'Stock ticker symbol (e.g., AAPL, MSFT)',
          },
        },
        {
          name: 'exchange',
          label: 'Exchange Symbol',
          type: 'text',
          admin: {
            description: 'Stock exchange code (e.g., NYSE, NASDAQ, LSE)',
          },
        },
        {
          name: 'relevance',
          label: 'Relevance Level',
          type: 'select',
          options: [
            { label: 'High', value: 'high' },
            { label: 'Medium', value: 'medium' },
            { label: 'Low', value: 'low' },
          ],
          defaultValue: 'medium',
        },
        {
          name: 'confidence',
          label: 'Confidence Score',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 100,
          admin: {
            description: 'AI confidence score (0-100)',
          },
        },
        {
          name: 'featured',
          label: 'Featured Company',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Mark as a featured company for prominent display',
          },
        },
      ],
      admin: {
        position: 'sidebar',
        description: 'Companies extracted from the article content',
      },
    },
    // Publishing metadata
    {
      name: 'publishedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        position: 'sidebar',
        description: 'User who published this article',
      },
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When this article was published',
      },
    },
    // Enhancement flag at bottom of sidebar
    {
      name: 'hasBeenEnhanced',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if content has been AI-enhanced',
      },
    },
    // German translation flag at bottom of sidebar
    {
      name: 'hasGermanTranslation',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if German translation is available',
      },
    },
    // ✅ SPRINT 5: Original source flag for tab visibility control
    {
      name: 'hasOriginalSource',
      type: 'checkbox',
      label: 'Has Original Source',
      admin: {
        description:
          'Indicates if this article originated from RSS feed and has source content',
        readOnly: true, // Set programmatically, not user-editable
        position: 'sidebar',
      },
      defaultValue: false,
    },
    // Tab structure
    {
      type: 'tabs',
      tabs: [
        {
          label: 'English Content',
          name: 'englishTab',
          fields: [
            {
              name: 'enhancedTitle',
              label: 'Enhanced English Title',
              type: 'text',
              admin: {
                description:
                  'AI-enhanced English title optimised for international markets',
              },
            },
            {
              name: 'enhancedSummary',
              label: 'Enhanced English Summary',
              type: 'textarea',
              admin: {
                description: 'AI-enhanced English summary (100-150 characters)',
              },
            },
            {
              type: 'collapsible',
              label: 'Enhanced English Content',
              admin: {
                initCollapsed: true,
                description:
                  'AI-enhanced English content optimised for international markets (600-750 words)',
              },
              fields: [
                // FIXED: Optimized Lexical field hooks
                {
                  name: 'enhancedContent',
                  label: 'Content',
                  type: 'richText',
                  editor: defaultLexical,
                  admin: {
                    description:
                      'Enhanced English content for international markets',
                  },
                  hooks: {
                    afterRead: [
                      ({ value }) => {
                        try {
                          return prepareLexicalForReading(value);
                        } catch (error) {
                          console.error('Error in Lexical afterRead:', error);
                          return value;
                        }
                      },
                    ],
                    beforeChange: [
                      ({ value }) => {
                        try {
                          // Only process if value needs normalization
                          if (!value || typeof value === 'string') {
                            return prepareLexicalForStorage(value);
                          }
                          return value;
                        } catch (error) {
                          console.error(
                            'Error in Lexical beforeChange:',
                            error
                          );
                          return value;
                        }
                      },
                    ],
                  },
                },
              ],
            },
            {
              name: 'keywords',
              label: 'Enhanced Keywords',
              type: 'array',
              fields: [
                {
                  name: 'keyword',
                  type: 'text',
                },
              ],
              admin: {
                description: 'AI-enhanced keywords for English content',
              },
            },
            {
              name: 'enhancedKeyInsights',
              label: 'Enhanced Key Insights',
              type: 'array',
              fields: [
                {
                  name: 'insight',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                description:
                  'AI-enhanced key insights highlighting the most important takeaways',
              },
            },
          ],
        },
        {
          label: 'Sources',
          name: 'sourcesTab',
          // ✅ SPRINT 5: Conditional tab visibility based on article origin
          admin: {
            condition: (data: any) => {
              // Show Sources tab for:
              // 1. Generated articles (always have original source)
              // 2. Curated articles that were converted from generated (hasOriginalSource = true)
              return (
                data.articleType === 'generated' ||
                (data.articleType === 'curated' &&
                  data.hasOriginalSource === true)
              );
            },
          },
          fields: [
            // RSS Feed Source - shows which feed this article came from
            {
              name: 'sourceFeed',
              label: 'Source RSS Feed',
              type: 'relationship',
              relationTo: 'rss-feeds',
              admin: {
                readOnly: true,
                description: 'The RSS feed that provided this article',
              },
            },
            // Source URL moved from sidebar to Sources tab
            {
              name: 'sourceUrl',
              label: 'Source URL',
              type: 'text',
              admin: {
                description: 'Original article URL from RSS feed',
              },
            },
            {
              name: 'originalPublishedAt',
              label: 'Source Published Date',
              type: 'date',
              admin: {
                readOnly: true,
                date: {
                  pickerAppearance: 'dayAndTime',
                },
                description: 'When the original source article was published',
              },
            },
            {
              name: 'originalTitle',
              label: 'Original Title',
              type: 'text',
              admin: {
                readOnly: true,
                description: 'Original article title from the source',
              },
            },
            {
              name: 'originalSummary',
              label: 'Original Summary',
              type: 'textarea',
              admin: {
                readOnly: true,
                description:
                  'Original article summary from Firecrawl extraction',
              },
            },
            {
              type: 'collapsible',
              label: 'Original Content',
              admin: {
                initCollapsed: true,
                description: 'Original scraped content from source article',
              },
              fields: [
                // FIXED: Minimal hooks for read-only content
                {
                  name: 'originalContent',
                  label: 'Content',
                  type: 'richText',
                  editor: defaultLexical,
                  admin: {
                    readOnly: true,
                    description:
                      'Original scraped content from Firecrawl, converted to Lexical format',
                  },
                  hooks: {
                    afterRead: [
                      ({ value }) => {
                        try {
                          return prepareLexicalForReading(value);
                        } catch (error) {
                          console.error(
                            'Error in original content afterRead:',
                            error
                          );
                          return value;
                        }
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
        // FIXED: Proper conditional tab visibility
        {
          label: 'German Translation',
          name: 'germanTab',
          admin: {
            condition: data => data?.hasGermanTranslation === true,
          },
          fields: [
            {
              name: 'germanTitle',
              label: 'German Title',
              type: 'text',
              admin: {
                description:
                  'AI-translated German title. This is the main title of the article.',
              },
            },
            {
              name: 'germanSummary',
              label: 'German Summary',
              type: 'textarea',
              admin: {
                description:
                  'AI-translated German summary (150-200 words). Used in the main Article Desc',
              },
            },
            // FIXED: Consistent Lexical hook pattern
            {
              name: 'germanContent',
              label: 'German Content',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                description: 'AI-translated German content (600-750 words)',
              },
              hooks: {
                afterRead: [
                  ({ value }) => {
                    try {
                      return prepareLexicalForReading(value);
                    } catch (error) {
                      console.error(
                        'Error in German content afterRead:',
                        error
                      );
                      return value;
                    }
                  },
                ],
                beforeChange: [
                  ({ value }) => {
                    try {
                      if (!value || typeof value === 'string') {
                        return prepareLexicalForStorage(value);
                      }
                      return value;
                    } catch (error) {
                      console.error(
                        'Error in German content beforeChange:',
                        error
                      );
                      return value;
                    }
                  },
                ],
              },
            },
            {
              name: 'germanKeywords',
              label: 'German Keywords',
              type: 'array',
              fields: [
                {
                  name: 'keyword',
                  type: 'text',
                },
              ],
              admin: {
                description: 'AI-translated German keywords',
              },
            },
            {
              name: 'germanKeyInsights',
              label: 'German Key Insights',
              type: 'array',
              fields: [
                {
                  name: 'insight',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                description: 'AI-translated German key insights',
              },
            },
            // Translation quality metrics removed - no longer needed
          ],
        },
        {
          label: 'SEO',
          name: 'meta',
          fields: [
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaDescriptionField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
              hasGenerateFn: true,
            }),
            PreviewField({
              hasGenerateFn: true,
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
          ],
        },
      ],
    },
  ],
  // CONSOLIDATED: All collection-level hooks in one place
  hooks: {
    beforeChange: [
      async ({ data, operation, req, originalDoc }) => {
        try {
          // 🔍 SPRINT 3: Debug Lexical content reception
          if (data?.englishTab?.enhancedContent) {
            console.log('🔍 SPRINT 3: Lexical Content Debug:', {
              operation,
              hasEnhancedContent: !!data.englishTab.enhancedContent,
              contentType: typeof data.englishTab.enhancedContent,
              contentStructure: JSON.stringify(
                data.englishTab.enhancedContent,
                null,
                2
              ),
              textExtraction:
                data.englishTab.enhancedContent?.root?.children?.[0]
                  ?.children || 'no-text-nodes',
            });
          }

          // Debug logging for native publish detection
          const isNativePublishing =
            data._status === 'published' &&
            originalDoc?._status !== 'published';

          if (isNativePublishing) {
            console.log('🔍 NATIVE PUBLISH DETECTED for article:', data.title);
            console.log(
              '📊 Current status:',
              originalDoc?._status,
              '→',
              data._status
            );
            console.log('📋 Workflow stage:', data.workflowStage);
          }

          // ✅ SPRINT 5: Handle article type switching and hasOriginalSource field
          if (operation === 'update' && originalDoc) {
            const wasGenerated = originalDoc.articleType === 'generated';
            const nowCurated = data.articleType === 'curated';

            // If switching from generated to curated, preserve hasOriginalSource
            if (wasGenerated && nowCurated) {
              data.hasOriginalSource = true;

              console.log('🔄 Article Type Switch:', {
                from: 'generated',
                to: 'curated',
                preservingSourceAccess: true,
                articleId: originalDoc.id,
              });
            }

            // If creating new curated article, ensure hasOriginalSource is false
            if (
              data.articleType === 'curated' &&
              !originalDoc.hasOriginalSource
            ) {
              data.hasOriginalSource = false;
            }

            // Generated articles always have original source
            if (data.articleType === 'generated') {
              data.hasOriginalSource = true;
            }
          }

          // For new articles
          if (operation === 'create') {
            data.hasOriginalSource = data.articleType === 'generated';
          }

          // Validate article for publication before processing
          const validationResult = validateArticleForPublication({
            data,
            operation,
            req,
            originalDoc, // beforeChange has access to originalDoc for state transition detection
          });

          // Debug validation result
          if (isNativePublishing) {
            console.log('✅ Validation result:', {
              isValid: validationResult.isValid,
              errorCount: validationResult.errors.length,
              warningCount: validationResult.warnings.length,
            });
          }

          // If validation fails, throw an error to prevent save
          if (!validationResult.isValid) {
            console.error(
              '🚫 BLOCKING PUBLISH - Validation failed:',
              validationResult.errors
            );
            throw formatValidationError(validationResult);
          }

          // Log any warnings
          logValidationWarnings(validationResult, data?.title);

          // Set published metadata when transitioning to published via native system
          const isPublishing =
            data?._status === 'published' &&
            originalDoc?._status !== 'published';

          if (isPublishing) {
            // Set published metadata only if not already set
            if (!data.publishedAt) {
              data.publishedAt = new Date().toISOString();
            }
            if (!data.publishedBy && req?.user?.id) {
              data.publishedBy = req.user.id;
            }
          }

          // ✅ PHASE 2: Auto-calculate reading time when content changes
          if (data) {
            const germanContent = data.germanTab?.germanContent;
            const englishContent = data.englishTab?.enhancedContent;

            // Calculate reading time if content exists (prioritising German, then English)
            if (germanContent || englishContent) {
              const readTimeMinutes = calculateArticleReadingTime(
                germanContent || englishContent
              );
              if (readTimeMinutes > 0) {
                data.readTimeMinutes = readTimeMinutes;
              }
            }
          }

          return data;
        } catch (error) {
          console.error('Error in collection beforeChange hook:', error);
          // Re-throw validation errors to prevent save
          throw error;
        }
      },
    ],
    // ✅ PHASE 2: Add cache invalidation when articles change
    afterChange: [createArticleInvalidationHook()],
    afterDelete: [
      async ({ doc, req }) => {
        // Import server-only functions within the hook context
        const { revalidateTag, revalidatePath } = await import('next/cache');

        try {
          // Only invalidate cache if not explicitly disabled
          if (!req.context?.disableRevalidate) {
            // Always invalidate cache when deleting published articles
            if (doc?._status === 'published') {
              req.payload.logger.info(
                `Invalidating homepage cache for deleted article: ${doc.title}`
              );

              // Clear homepage cache
              revalidatePath('/');

              // Clear all article caches
              revalidateTag('articles');

              // Clear specific tier caches
              revalidateTag('tier-1');
              revalidateTag('tier-2');
              revalidateTag('tier-3');

              // Clear layout cache for tier-1 advanced layout
              revalidateTag('layout');

              // Clear tier-specific cache if placement was set
              if (doc.placement) {
                revalidateTag(`tier-${doc.placement.split('-')[1]}`);
              }
            }
          }
        } catch (error) {
          // Don't fail the delete if cache invalidation fails
          console.error('Error in cache invalidation hook:', error);
        }

        return doc;
      },
    ],
  },
  // Enable versioning with drafts for preview functionality
  // Using both workflowStage field and native drafts for comprehensive lifecycle management
  versions: {
    drafts: true,
  },
};

export default Articles;
