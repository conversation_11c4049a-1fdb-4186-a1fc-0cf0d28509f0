import type { CollectionConfig } from 'payload';
import { authenticated } from '../../access/authenticated';
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished';
import { slugField } from '../../fields/slug';
import { defaultLexical } from '../../fields/defaultLexical';
import { revalidatePage, revalidateDelete } from './hooks/revalidatePage';
import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields';

export const Pages: CollectionConfig = {
  slug: 'pages',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    title: true,
    slug: true,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', '_status', 'updatedAt'],
    description:
      'Manage static pages for the website. Create pages like About Us, Contact, Privacy Policy, etc.',
    group: 'Content',
  },
  fields: [
    // Top-level title field (required by PayloadCMS for useAsTitle)
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        hidden: true, // Hide from form since we'll use englishTab.title for editing
      },
    },

    // Translation controls in sidebar
    {
      type: 'ui',
      name: 'translationControls',
      admin: {
        position: 'sidebar',
        components: {
          Field:
            '@/components/admin/pages/PageDocumentControls#PageDocumentControls',
        },
      },
    },

    // German translation flag
    {
      name: 'hasGermanTranslation',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if German translation is available',
      },
    },

    // Tab structure with English and German content
    {
      type: 'tabs',
      tabs: [
        {
          label: 'English Content',
          name: 'englishTab',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
              admin: {
                description: 'English page title for translation',
              },
            },
            {
              name: 'content',
              type: 'richText',
              editor: defaultLexical,
              required: true,
              admin: {
                description: 'The main English content of the page',
              },
            },
          ],
        },
        {
          label: 'German Translation',
          name: 'germanTab',
          admin: {
            condition: data => data?.hasGermanTranslation === true,
          },
          fields: [
            {
              name: 'germanTitle',
              label: 'German Title',
              type: 'text',
              admin: {
                description: 'AI-translated German title for the page',
              },
            },
            {
              name: 'germanContent',
              label: 'German Content',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                description: 'AI-translated German content for the page',
              },
            },
          ],
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
            }),
            MetaDescriptionField({}),
            PreviewField({
              hasGenerateFn: true,
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
          ],
        },
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
        description: 'When this page was published',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [
      // Sync englishTab.title to top-level title for admin display
      ({ data }) => {
        if (data.englishTab?.title) {
          data.title = data.englishTab.title;
        }
        return data;
      },
    ],
    afterChange: [revalidatePage],
    afterDelete: [revalidateDelete],
  },
  versions: {
    drafts: true, // Simple drafts like Articles - manual save behavior
  },
};
