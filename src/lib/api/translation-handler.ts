/**
 * Generic Translation API Handler
 *
 * Provides standardized translation API functionality for any PayloadCMS collection
 * that supports bilingual English/German content. Extracts common patterns from
 * the Articles translation system for reuse.
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-01-27
 */

import config from '@payload-config';
import { getPayload } from 'payload';
import { NextResponse } from 'next/server';
import {
  translationService,
  type TranslationCollection,
} from '@/lib/services/translation-service';

/**
 * Translation request body structure
 */
export interface TranslationRequest {
  documentId: string;
  collection?: TranslationCollection;
}

/**
 * Standardized API response format
 */
export interface StandardAPIResponse {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  metrics?: {
    processingTime: number;
    tokenUsage?: number;
    costEstimate?: number;
    linguisticAccuracy?: number;
    culturalAdaptation?: number;
  };
}

/**
 * Generic Translation Handler Class
 */
export class TranslationHandler {
  private collection: TranslationCollection;

  constructor(collection: TranslationCollection) {
    this.collection = collection;
  }

  /**
   * Handle translation request
   */
  async handleTranslation(
    requestBody: TranslationRequest,
    options?: { timeout?: number }
  ): Promise<NextResponse> {
    const startTime = Date.now();

    try {
      const { documentId } = requestBody;

      if (!documentId) {
        console.error(
          `❌ No documentId provided in ${this.collection} translation request`
        );
        return NextResponse.json(
          { success: false, error: 'Document ID is required' },
          { status: 400 }
        );
      }

      console.log(
        `🚀 Starting German translation for ${this.collection}: ${documentId}`
      );
      console.log(
        '📋 Working with saved database content only (save-first workflow)'
      );

      const payload = await getPayload({ config });

      // 🔧 DATABASE TIMING FIX: Ensure fresh database read after save
      // Small delay to allow form save to fully commit to database before reading
      // This prevents race condition where translate reads stale data immediately after save
      await new Promise(resolve => setTimeout(resolve, 500));

      // Fetch the document to get existing data and validate eligibility
      const document = await payload.findByID({
        collection: this.collection,
        id: documentId,
        // Override access control for internal API operations
        overrideAccess: true,
      });

      if (!document) {
        console.error(`❌ ${this.collection} not found with ID: ${documentId}`);
        return NextResponse.json(
          { success: false, error: `${this.collection} not found` },
          { status: 404 }
        );
      }

      console.log(
        `📄 ${this.collection} found: ${document.title || 'Untitled'}`
      );

      // Extract content using the translation service
      const translatableContent = translationService.extractContent(
        document,
        this.collection
      );

      console.log('🔍 Extracted content for translation:', {
        collection: this.collection,
        title: translatableContent.title,
        hasContent: !!translatableContent.content,
        hasGermanTranslation: translatableContent.hasGermanTranslation,
      });

      // Validate content is ready for translation
      const validation =
        translationService.validateForTranslation(translatableContent);

      if (!validation.isValid) {
        console.error('❌ Content validation failed:', validation.errors);
        return NextResponse.json(
          {
            success: false,
            error: `Content validation failed: ${validation.errors.join(', ')}`,
          },
          { status: 400 }
        );
      }

      // Allow re-translation if German translation already exists
      const isReTranslation = !!translatableContent.hasGermanTranslation;
      if (isReTranslation) {
        console.log('🔄 Re-translating existing German content');
      } else {
        console.log('🆕 Initial German translation');
      }

      // Perform translation using the generic service
      const translationResult = await translationService.translateContent(
        translatableContent,
        {
          temperature: 0.3, // Lower temperature for more literal translations
          includeProcessingMetadata: true,
        }
      );

      if (!translationResult.success || !translationResult.data) {
        console.error('❌ German translation failed:', translationResult.error);
        return NextResponse.json(
          {
            success: false,
            error: `German translation failed: ${translationResult.error}`,
          },
          { status: 500 }
        );
      }

      console.log('✅ German translation successful');
      console.log(
        `📊 Performance: ${translationResult.metrics.processingTime}ms`
      );

      // Create update data using the translation service
      const updateData = translationService.createUpdateData(
        translationResult.data,
        this.collection
      );

      // Preserve important existing fields during translation
      this.preserveExistingFields(document, updateData);

      // Update workflow stage for articles (pages don't have workflow stages)
      if (this.collection === 'articles') {
        const articleDoc = document as any; // Type assertion for articles
        if (articleDoc.workflowStage === 'candidate-article') {
          updateData.workflowStage = 'translated';
        }
      }

      // Update the document
      const updatedDocument = await payload.update({
        collection: this.collection,
        id: documentId,
        data: updateData,
      });

      console.log(
        `✅ German ${isReTranslation ? 're-' : ''}translation completed for ${this.collection} ${documentId}`
      );

      // Prepare standardized API response
      const responseData = this.createResponseData(
        translationResult,
        updateData,
        document
      );

      return NextResponse.json({
        success: true,
        message: `${this.collection} ${isReTranslation ? 're-' : ''}translated successfully`,
        data: responseData,
        metrics: {
          processingTime: translationResult.metrics.processingTime,
          tokenUsage: translationResult.metrics.tokenUsage,
          costEstimate: translationResult.metrics.costEstimate,
          linguisticAccuracy: translationResult.metrics.linguisticAccuracy,
          culturalAdaptation: translationResult.metrics.culturalAdaptation,
        },
      });
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      console.error(`❌ Error translating ${this.collection}:`, error);

      return NextResponse.json(
        {
          success: false,
          error:
            error.message ||
            `Unknown error occurred during ${this.collection} translation`,
          metrics: {
            processingTime,
            tokenUsage: 0,
            costEstimate: 0,
          },
        },
        { status: 500 }
      );
    }
  }

  /**
   * Preserve important existing fields during translation
   */
  private preserveExistingFields(document: any, updateData: any): void {
    // Common fields to preserve
    const fieldsToPreserve = [
      'featuredImage',
      'categories',
      'placement',
      'pinned',
      'trending',
      'publishedBy',
      'publishedAt',
    ];

    fieldsToPreserve.forEach(field => {
      if (document[field] !== undefined) {
        updateData[field] = document[field];
      }
    });

    // Articles-specific fields
    if (this.collection === 'articles') {
      const articleFields = ['relatedCompanies', 'hasBeenEnhanced'];
      articleFields.forEach(field => {
        if (document[field] !== undefined) {
          updateData[field] = document[field];
        }
      });
    }

    // Pages-specific fields - add any page-specific preservation logic here
    if (this.collection === 'pages') {
      // Pages might have specific fields to preserve in the future
    }
  }

  /**
   * Create standardized response data
   */
  private createResponseData(
    translationResult: any,
    updateData: any,
    originalDocument: any
  ): any {
    const mapping = translationService.getFieldMapping(this.collection);

    // Base response structure
    const responseData: any = {
      // Translation flag
      [mapping.hasTranslationFlag]: true,
    };

    // Create german tab structure for form updates
    if (translationResult.data) {
      responseData.germanTab = {
        germanTitle: translationResult.data.germanTitle,
        germanContent: translationResult.data.germanContent,
      };

      // Add summary if supported
      if (translationResult.data.germanSummary) {
        responseData.germanTab.germanSummary =
          translationResult.data.germanSummary;
      }

      // Articles-specific fields
      if (
        this.collection === 'articles' &&
        translationResult.data.germanKeyInsights
      ) {
        responseData.germanTab.germanKeyInsights =
          translationResult.data.germanKeyInsights;
      }

      if (
        this.collection === 'articles' &&
        translationResult.data.germanKeywords
      ) {
        responseData.germanTab.germanKeywords =
          translationResult.data.germanKeywords;
      }
    }

    // Workflow stage updates for articles
    if (this.collection === 'articles') {
      const articleDoc = originalDocument as any; // Type assertion for articles
      if (articleDoc.workflowStage === 'candidate-article') {
        responseData.workflowStage = 'translated';
      }
    }

    return responseData;
  }

  /**
   * Validate request format
   */
  static validateRequest(body: any): { isValid: boolean; error?: string } {
    if (!body || typeof body !== 'object') {
      return {
        isValid: false,
        error: 'Request body must be a valid JSON object',
      };
    }

    if (!body.documentId) {
      return { isValid: false, error: 'documentId is required' };
    }

    if (typeof body.documentId !== 'string') {
      return { isValid: false, error: 'documentId must be a string' };
    }

    return { isValid: true };
  }
}

/**
 * Create translation handler for specific collection
 */
export function createTranslationHandler(
  collection: TranslationCollection
): TranslationHandler {
  return new TranslationHandler(collection);
}

/**
 * Generic translation API route handler
 * Can be used in any collection's translation API route
 */
export async function handleTranslationRequest(
  request: Request,
  collection: TranslationCollection
): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate request format
    const validation = TranslationHandler.validateRequest(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    // Create and use translation handler
    const handler = createTranslationHandler(collection);
    return await handler.handleTranslation(body);
  } catch (error) {
    console.error(`❌ Error parsing ${collection} translation request:`, error);
    return NextResponse.json(
      { success: false, error: 'Invalid request format' },
      { status: 400 }
    );
  }
}
